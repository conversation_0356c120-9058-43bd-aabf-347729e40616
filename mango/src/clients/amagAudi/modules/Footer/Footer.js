/* eslint-disable max-len */
/* @flow */
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import { Link } from '../../../../tango/routing';
import ExternalLink from '../../../../modules/external-link';
import { RouteProps } from '../../../../tango/routing/types';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import withDealer, {
	type DealerProps,
} from '../../../amagCore/modules/withDealer';
import withDealerBrandWebsiteConfig, {
	type DealerBrandWebsiteConfigProps,
} from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import { namespace } from './i18n';
import {
	getDealerCapabilities,
	type DealerCapabilities,
} from '../dealer-config';
import { trackGoogleAnalyticConversion } from '../../../amagCore/modules/tracking/tracking';
import {
	AUDI_SERVICE_APPOINTMENT_LINK_CONVERSION_ID,
	GTM_CLASS_MAPPING,
} from '../../../amagCore/modules/tracking/constants';
import {
	getDealerHasNewRetailerLayout,
	getDealerId,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import {
	externalUrls,
	NEW_RETAIL_PAGE_DEALERS_WHITELIST,
} from '../../../amagCore/modules/constants';
import { CONFIG } from '../../modules/tracking/constants';

import { Service, Mail, Test, Key, DamageReport } from '../../assets/svg';
import styles from './Footer.css';
import { isExternalLink } from '../../../../tango/link';
import { PrepareLink } from '../../../amagCore/modules/DealerConfigProvider/prepareLinkItem';
import { DAMAGE_REPORT_ALIAS } from '../../modules/constants';

type InternalProps = {
	dealerCapabilities: DealerCapabilities,
	serviceAppointmentUrl: ?string,
	hasNewRetailerLayout: boolean,
	testDriveUrl?: string,
	damageReportUrl?: string,
};

type Props = InternalProps &
	LocalizeProps &
	DealerProps &
	DealerBrandWebsiteConfigProps &
	RouteProps;

function renderSocialLinks(social) {
	const links = social.map((link, index) => (
		<li key={index}>
			<ExternalLink href={link.get('url')}>
				<span className={styles[link.get('type')]} />
			</ExternalLink>
		</li>
	));

	return <ul className={styles.socialLinks}>{links}</ul>;
}

function Footer(props: Props) {
	const {
		t,
		locale,
		social,
		dealerCapabilities,
		serviceAppointmentUrl,
		testDriveUrl,
		damageReportUrl,
		hasNewRetailerLayout,
		dealerId,
	} = props;

	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		const handleResize = () => {
			setIsMobile(window.innerWidth <= 768);
		};

		window.addEventListener('resize', handleResize);
		handleResize();

		return () => {
			window.removeEventListener('resize', handleResize);
		};
	}, []);

	const googleConversionId = AUDI_SERVICE_APPOINTMENT_LINK_CONVERSION_ID;
	const damageReportLink = damageReportUrl || DAMAGE_REPORT_ALIAS;
	const isDamageReportInternalLink = !damageReportUrl;

	const getPartnerPrivacyLink = (dealerIdentifier: string, lang: string) => {
		switch (dealerIdentifier) {
			case 'CHE049':
				return `https://www.ppautotreff.ch/${lang}/datenschutzerklaerung`;
			case '0000000030':
				return `https://www.autokaeppeli.ch/${lang}/datenschutzerklaerung`;
			case '0000000522':
				return `https://www.gng.ch/${locale}/datenschutzerklaerung`;
			case 'CHE024':
				return `https://www.sennautos.ch/${locale}/protection-des-donnees`;
			default:
				return '';
		}
	};

	const serviceAppointmentUrlElement = (
		serviceAppointmentVar,
		hasNewRetailerLayoutVar,
		googleConversionIdVar,
	) =>
		serviceAppointmentVar && (
			<li>
				<ExternalLink
					href={serviceAppointmentVar}
					onClick={() => trackGoogleAnalyticConversion(googleConversionIdVar)}
				>
					<Service />
					<strong>
						{hasNewRetailerLayoutVar
							? t('retailerScheduleService')
							: t('scheduleService')}
					</strong>
				</ExternalLink>
			</li>
		);

	const serviceAppointmentFormElement = (
		serviceAppointmentVar,
		hasNewRetailerLayoutVar,
		googleConversionIdVar,
	) =>
		hasNewRetailerLayoutVar &&
		!serviceAppointmentVar && (
			<li>
				<Link
					to="serviceappointmentform"
					onClick={() => trackGoogleAnalyticConversion(googleConversionIdVar)}
				>
					<Service />
					<strong>{t('scheduleService')}</strong>
				</Link>
			</li>
		);

	const testDriveElement = (
		isServicePartnerOnlyVar,
		hasNewRetailerLayoutVar,
		testDriveUrlVar,
		dealerIdParam,
	) =>
		!isServicePartnerOnlyVar &&
		!hasNewRetailerLayoutVar && (
			<React.Fragment>
				<li>
					{isExternalLink(testDriveUrlVar) ? (
						<Link
							className={GTM_CLASS_MAPPING.testDrive}
							to="testdrive"
							onClick={() => {
								if (
									CONFIG.some(
										conf => conf.dealerId === dealerIdParam && conf.active,
									)
								) {
									window.gtag('event', 'conversion', {
										send_to: 'AW-875904358/ZYatCKDoi9MBEOb61KED',
									});
								}
							}}
						>
							<Test />
							<strong>{t('testDrive')}</strong>
						</Link>
					) : (
						<PrepareLink uri={testDriveUrlVar} locale={locale}>
							<Test />
							<strong>{t('testDrive')}</strong>
						</PrepareLink>
					)}
				</li>
			</React.Fragment>
		);

	const desktopLinks = (
		serviceAppointmentUrlParam,
		hasNewRetailerLayoutParam,
		isServicePartnerOnly,
		testDriveUrlParam,
		localeParam,
		dealerIdParam,
		googleConversionIdParam,
	) => (
		<React.Fragment>
			{serviceAppointmentUrlElement(
				serviceAppointmentUrlParam,
				hasNewRetailerLayoutParam,
				googleConversionIdParam,
			)}
			{serviceAppointmentFormElement(
				serviceAppointmentUrlParam,
				hasNewRetailerLayoutParam,
				googleConversionIdParam,
			)}
			{testDriveElement(
				isServicePartnerOnly,
				hasNewRetailerLayoutParam,
				testDriveUrlParam,
				localeParam,
				dealerIdParam,
			)}
			<li>
				<Link to="contact">
					<Mail />
					<strong>{t('contact')}</strong>
				</Link>
			</li>
			<li>
				<ExternalLink href="https://my.audi.com/">
					<Key />
					<strong>{t('myAudi')}</strong>
				</ExternalLink>
			</li>
		</React.Fragment>
	);

	return (
		<div className={styles.footer}>
			<div className={styles.wrapper}>
				<ul className={styles.iconMenu}>
					{!isMobile &&
						desktopLinks(
							serviceAppointmentUrl,
							hasNewRetailerLayout,
							dealerCapabilities.isServicePartnerOnly,
							testDriveUrl,
							locale,
							dealerId,
							googleConversionId,
						)}
					<li className={styles.damageReport}>
						<PrepareLink
							uri={damageReportLink}
							isInternalLink={isDamageReportInternalLink}
							locale={locale}
						>
							<DamageReport />
							<strong>{t('damageReport')}</strong>
						</PrepareLink>
					</li>
				</ul>
				<ul className={styles.boringMenu}>
					<li>
						<Link to="legalNotice">{t('legal')}</Link>
					</li>
					<li>
						<ExternalLink href={externalUrls.audi.dataProtection[locale]}>
							{t('privacy')}
						</ExternalLink>
					</li>
					<li>
						<ExternalLink href={externalUrls.audi.dataProtectionInformation[locale]}>
							{t('dataProtectionInformation')}
						</ExternalLink>
					</li>
					{getPartnerPrivacyLink(dealerId, locale) && (
						<li>
							<ExternalLink href={getPartnerPrivacyLink(dealerId, locale)}>
								{t('partnerPrivacy')}
							</ExternalLink>
						</li>
					)}
					{NEW_RETAIL_PAGE_DEALERS_WHITELIST.includes(dealerId) && (
						<li>
							<Link to="imprint">{t('impressum')}</Link>
						</li>
					)}
					{renderSocialLinks(social)}
				</ul>

				<p className={styles.mt0}>{t('copy1')}</p>
				<p className={`${styles.mt0} ${styles.mb0}`}>{t('copy2')}</p>
			</div>
		</div>
	);
}

function mapStateToProps(state: ReduxState) {
	return {
		dealerCapabilities: getDealerCapabilities(state),
		hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
		dealerId: getDealerId(state),
	};
}

const withHOCs = combineHOCs([
	withDealerBrandWebsiteConfig,
	withDealer,
	withStyles(styles),
	localize(namespace),
	connect(mapStateToProps),
]);

export default withHOCs(Footer);
