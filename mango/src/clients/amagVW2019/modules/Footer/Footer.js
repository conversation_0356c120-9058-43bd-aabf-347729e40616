/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import classNames from 'classnames';
import { List } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import ExternalLink from '../../../../modules/external-link';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import withDealer from '../../../amagCore/modules/withDealer';
import {
	getDealerIsAmag,
	getDealerMapLocations,
	getDealerId,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import Link from '../../../../tango/routing/Link';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { AddressAccordion } from '../../../amagCore/modules/footer';
import OpeningHours from '../../../amagCore/modules/contact/OpeningHours';
import {
	externalUrls,
	NEW_RETAIL_PAGE_DEALERS_WHITELIST,
} from '../../../amagCore/modules/constants';

import styles from './footer.css';

const socialCaptions = {
	facebook: 'Facebook',
	youtube: 'YouTube',
	twitter: 'Twitter',
	gplus: 'TikTok',
	instagram: 'Instagram',
	xing: 'Xing',
	linkedin: 'Linkedin',
};

type Props = {
	social: List<*>,
	brand: string,
	locations: any,
};

type footerItemProps = {
	route: string,
	caption: string,
	external: boolean,
};

type socialIconProps = {
	url: string,
	type: string,
};

function FooterItem(props: footerItemProps) {
	return (
		<li>
			{props.external ? (
				<ExternalLink href={props.route} activeClassName="linkRouteActive">
					{props.caption}
				</ExternalLink>
			) : (
				<Link to={props.route} activeClassName="linkRouteActive">
					{props.caption}
				</Link>
			)}
		</li>
	);
}

function SocialIcon(props: socialIconProps) {
	return (
		<li>
			<ExternalLink href={props.url} target="_blank">
				{props.type && <span id="social" className={props.type} />}
				{socialCaptions[props.type]}
			</ExternalLink>
		</li>
	);
}

function getPriceListLinks(brand) {
	if (brand === 'volkswagen') {
		return {
			de:
				'https://www.volkswagen.ch/de/beratung-und-kauf/kataloge-und-preislisten.html',
			fr:
				'https://www.volkswagen.ch/fr/conseil-et-achat/catalogues-et-listes-de-prix.html',
			it:
				'https://www.volkswagen.ch/it/consulenza-e-acquisto/cataloghi-e-listini-prezzi.html',
		};
	}

	return {
		de:
			'https://www.volkswagen-nutzfahrzeuge.ch/de/angebote-und-kauf/katalog-preislisten.html',
		fr:
			'https://www.volkswagen-nutzfahrzeuge.ch/fr/offres-et-achats/catalogues-prix.html',
		it:
			'https://www.volkswagen-nutzfahrzeuge.ch/it/offerta-e-acquisto/cataloghi-prezzi.html',
	};
}

function getDataProtectionLinks(brand) {
	return externalUrls[brand].dataProtection;
}

function handleLegalItems(dealerId, locale, t, brand) {
	const legalItems = [
		{
			key: 'priceList',
			route: getPriceListLinks(brand)[locale],
			caption: t('amag.views.footer.priceList'),
			external: true,
		},
		{
			key: 'contact',
			route: 'contact',
			caption: t('amag.views.footer.openingHours'),
		},
		{
			key: 'legalNotice',
			route: 'legalNotice',
			caption: t('amag.views.footer.legalNotice'),
		},
		{
			key: 'dataProtection',
			route: getDataProtectionLinks(brand)[locale],
			caption: t('amag.views.footer.dataProtection'),
			external: true,
		},
	];

	const dealerSpecificItems = {
		CHE035: [{ key: 'avb', route: 'avb', caption: t('amag.views.footer.avb') }],
		CHE049: [
			{
				key: 'partnerDataProtection',
				route: `https://www.ppautotreff.ch/${locale}/datenschutzerklaerung`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		'0000000745': [
			{
				key: 'partnerDataProtection',
				route: `https://www.auto-duenki.ch/${locale}/datenschutzerklaerung`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		'0000000718': [
			{
				key: 'partnerDataProtection',
				route: `https://www.dorfgarage-zumikon.ch/${locale}/datenschutzerklaerung`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		'0000000030': [
			{
				key: 'partnerDataProtection',
				route: `https://www.autokaeppeli.ch/${locale}/datenschutzerklaerung`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		'0000000522': [
			{
				key: 'partnerDataProtection',
				route: `https://www.gng.ch/${locale}/datenschutzerklaerung`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		CHE040: [
			{
				key: 'partnerDataProtection',
				route: 'https://www.garage-johann-frei.ch/datenschutz',
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		'0000000344': [
			{
				key: 'partnerDataProtection',
				route:
					'https://partner.volkswagen.ch/mattei/it/dichiarazione-sulla-protezione-dei-dati',
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		'0000000656': [
			{
				key: 'partnerDataProtection',
				route: `https://www.sommerhalder.ch/${locale}/datenschutzerklaerung`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
		CHE024: [
			{
				key: 'partnerDataProtection',
				route: `https://www.sennautos.ch/${locale}/protection-des-donnees`,
				caption: t('amag.views.footer.partnerDataProtection'),
				external: true,
			},
		],
	};

	if (dealerSpecificItems[dealerId]) {
		legalItems.push(...dealerSpecificItems[dealerId]);
	}

	if (NEW_RETAIL_PAGE_DEALERS_WHITELIST.includes(dealerId)) {
		legalItems.push({
			key: 'impressum',
			route: 'imprint',
			caption: 'Impressum',
		});
	}

	return legalItems;
}

function Footer({
	social,
	t,
	locations,
	locale,
	brand,
	dealerId,
}: Props & LocalizeProps) {
	const legalItems = handleLegalItems(dealerId, locale, t, brand);

	const departments =
		locations && locations.size === 1 && locations.first().get('departments');
	const filteredDepartments =
		departments &&
		departments.filter((v, k) => k === 'SERVICE' || k === 'SALES');

	return (
		<footer className={styles.footer}>
			<div className={styles.footerInner}>
				<div className={styles.flex}>
					<div
						className={classNames(styles.accordionContainer, {
							nz: brand !== 'volkswagen',
						})}
					>
						<AddressAccordion className={styles.addressAccordion} hideWebsite />
					</div>
					<div className={styles.section}>
						{filteredDepartments &&
							filteredDepartments.keySeq().map(departmentKey => {
								const department = filteredDepartments.get(departmentKey);
								return (
									<OpeningHours
										className={styles.openingHours}
										key={departmentKey}
										department={department}
									/>
								);
							})}
					</div>
					{social.size > 0 && (
						<div className={styles.section}>
							<h3>Social Media</h3>
							<ul className={styles.socials}>
								{social.map((item, index) => (
									<SocialIcon
										key={index}
										type={item.get('type')}
										url={item.get('url')}
									/>
								))}
							</ul>
						</div>
					)}
				</div>
				<div className={styles.horizontalSection}>
					<ul>
						{legalItems.map(item => (
							<FooterItem {...item} />
						))}
					</ul>
				</div>
			</div>
		</footer>
	);
}

function mapStateToProps(state: ReduxState) {
	return {
		locations: getDealerMapLocations(state),
		isAmag: getDealerIsAmag(state),
		dealerId: getDealerId(state),
	};
}

const withHocs = combineHOCs([
	withRouter,
	withDealer,
	localize(),
	connect(mapStateToProps),
	withStyles(styles),
]);

export default withHocs(Footer);
