@import theme(amagCoreTeam/teamMember);

.nameInput {
	all: initial;
	font-family: var(--amag-core-team-nameInputFontFamily);
	font-weight: bold;
	font-size: var(--amag-core-team-nameInputFontSize);
	line-height: var(--amag-core-team-nameInputLineHeight);
	width: 100%;
	display: inline-block;
}

.normalInput {
	font-family: var(--amag-core-team-inputFontFamily);
	font-size: var(--amag-core-team-inputFontSize);
	line-height: var(--amag-core-team-inputLineHeight);
	display: block;
}

.tagSelect {
	& select {
		border: 1px solid var(--amag-core-team-select-border-color);
		background-color: white;
	}
}

.editorForm {
	display: block;
	padding-top: 0;

	& ul {
		list-style: none;
		*zoom: 1;
		padding: 0 !important;
		margin: 0 !important;

		&:before,
		&:after {
			content: " ";
			display: table;
		}

		&:after {
			clear: both;
		}

		& li {
			float: left;
			margin-bottom: 10px;
			width: 49%;
			padding: 0;
			margin-right: 2%;
			position: relative;
			margin-left: 0;
			text-align: left;

			&.right {
				margin-left: 0;
				text-align: right;
				margin-right: 0;
			}

			&.fullWidth {
				padding: 0;
				width: 100%;
				text-align: center;
			}
		}
	}

	& label {
		font-weight: bold;
		text-align: left;
		width: 100%;
		display: block;
		@media (--screen-mobile) {
			display: none;
		}

		&.doubleHeight {
			height: 48px;
		}
	}

	& input[type="text"],
	& input[type="number"],
	& textarea,
	& select {
		margin: 0 !important;
		padding-top: 0;
		padding-bottom: 0;
		border-radius: 3px;
		line-height: 40px;
		height: 40px;
		@media (--screen-mobile) {
			line-height: 30px;
			height: 30px;
		}
		padding-top: 0;
		padding-bottom: 0;
	}

	& textarea {
		padding: 20px;
		height: 70px;
		line-height: 1.4;
		@media (--screen-mobile) {
			height: 40px;
			line-height: 1.2;
		}
		padding: 10px;
		border-radius: 3px;
	}
}

.editorImage {
	max-width: var(--amag-core-team-editor-image-max-width, 205px) !important;

	@media (--screen-mobile) {
		max-width: var(--amag-core-team-editor-image-max-width-mobile, 100px);
	}

	margin: 0 auto 24px !important;
	text-align: center;

	& img {
		padding: 10px;
		border: 1px solid var(--amag-core-team-editor-image-border-color);
		box-sizing: border-box;
	}
}
