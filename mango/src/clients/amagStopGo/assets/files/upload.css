@import '../variables.css';
@import '../images.css';

:root {

	/* image crop upload */
	--files-upload-cropButtonIcon: var(--iconCrop);
	--files-upload-cropButtonBackgroundColor: var(--white);
	--files-upload-hoverCropButtonBackgroundColor: color(var(--white) blackness(15%));

	--files-upload-doneCropButtonIcon: var(--iconDone);
	--files-upload-doneCropButtonBackgroundColor: var(--success-color);
	--files-upload-hoverDoneCropButtonBackgroundColor: color(var(--success-color) blackness(15%));
	--files-upload-hoverDoneCropButtonBorderColor: color(var(--success-color) blackness(15%));

	--files-upload-cancelCropButtonIcon: var(--iconCancel);
	--files-upload-cancelCropButtonBackgroundColor: var(--primary-color);
	--files-upload-hoverCancelCropButtonBackgroundColor: color(var(--primary-color) blackness(30%));
	--files-upload-cancelCropButtonHoverBorderColor: color(var(--primary-color) blackness(30%));

	--files-upload-deleteButtonIcon: var(--iconDelete);
	--files-upload-deleteButtonBackgroundColor: var(--white);
	--files-upload-hoverDeleteButtonBackgroundColor: color(var(--white) blackness(15%));
	--files-upload-deleteButtonHoverBorderColor: color(var(--white) blackness(15%));

	--files-upload-buttonIcon-size: 14px;
	--files-upload-buttonIcon-sizeSmaller: 10px;

	/* upload */
	--files-upload-dropzone-backgroundColor: color(var(--light-gray) lightness(90%));
	--files-upload-dropzone-hoverBackgroundColor: var(--light-gray);
	--files-upload-dropzone-borderColor: color(var(--primary-color) alpha(-50%));

	--files-upload-dropzone-uploadMessageFontFamily: var(--font-text);
	--files-upload-dropzone-uploadMessageFontSize: var(--default-font-size);
	--files-upload-dropzone-uploadMessageLineHeight: var(--default-line-height);

	--files-upload-dropzone-documnetUploadMessageIcon: var(--icon-document);
	--files-upload-dropzone-imageUploadMessageIcon: var(--iconPicture);

	--files-upload-dropzone-rcProgressCirclePath: var(--primary-color);

	--files-upload-dropzone-bottomDelete: 55px;
	--files-upload-dropzone-bottomCrop: 10px;
	--files-upload-dropzone-bottomDefault: 28px;

	--editorDraft-alignmentToolbar-textFlowWideWidth: 100%;
	--editorDraft-alignmentToolbar-textFlowWideLeft: 0;
	--editorDraft-alignmentToolbar-textFlowWideWidth-mobile: 100%;
	--editorDraft-alignmentToolbar-textFlowWideLeft-mobile: 0;

	--editorDraft-headerImageContainer-container: {
		clear: both;
	};

	--editorDraft-headerImageContainer-error-message: {
		clear: both;
	};

	--upload-dropzone-rejected-files: {
		clear: both;
	};

	--upload-dropzone-container: {
		& .dropzone {
			min-height: 420px !important;

			& .uploadMessage {
				&.imageUploadMessage {
					& svg {
						& :global(path.rc-progress-circle-path) {
							stroke: var(--primary-color);
						}
					}
				}
			}
		}
	};

	--editor-image-crop-mixin: {
		& .image {
			& img {
				display: flex;
			}
		}
	};

	--editorDraft-file-upload-buttons-mixin: {
		clear: both;
	};
}
