/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import moment from 'moment';
import combineHOCs from '../../../../../../tango/hoc/combineHOCs';
import { DateTimePicker } from '../../../../../../modules/formElements';
import { localize } from '../../../../../../modules/i18n';
import { LocalizeProps } from '../../../../../../modules/i18n/types';
import { isInfiniteDate, getInfiniteDate } from './Utils';
import styles from './DateTimePicker.css';
import { namespace } from '../i18n';

type ExternalProps = {
	value: ?string,
	onChange: Function,
	className?: string,
	minValue: ?string,
}

type InternalProps = LocalizeProps;

type Props = ExternalProps & InternalProps;

function UnpublishDateTimePicker(props: Props) {
	const {
		value,
		onChange,
		className,
		minValue,
		t,
	} = props;

	const defaultUnpublishDate = moment().add(90, 'days');

	if (value && isInfiniteDate(value) === false) {
		return (
			<div className={className}>
				<DateTimePicker
					value={value ? moment(value) : undefined}
					onChange={newValue =>
						onChange(newValue ? newValue.toISOString() : getInfiniteDate())}
					minDate={minValue ? moment(minValue) : moment()}
					minutesStep={15}
					containerClassName={styles.dateTimePickerContainer}
					className={styles.dateTimePickerInput}
					popperPlacement="top"
				/>
				<button
					className={styles.clearButton}
					type="button"
					onClick={() => onChange(getInfiniteDate())}
				>
					Clear
				</button>
			</div>
		);
	}

	/* eslint-disable jsx-a11y/no-static-element-interactions */
	return (
		<div
			className={classNames(className, styles.notSetDateLabel, styles.never)}
			onClick={() => onChange(defaultUnpublishDate.toISOString())}
			role="button"
		>
			{t('never')}
		</div>);
}

const withHocs = combineHOCs([
	localize(namespace),
	withStyles(styles),
]);

export default (withHocs(UnpublishDateTimePicker): ReactComponent<ExternalProps>);
