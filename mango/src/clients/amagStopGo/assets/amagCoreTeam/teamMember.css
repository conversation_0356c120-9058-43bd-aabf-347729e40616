@import '../variables.css';
@import '../images.css';
@import '../../modules/buttons/Button.css';

@custom-media --screen-team-mobile (max-width: 640px);

:root {
	--amag-core-team-primary-color: var(--primary-color);
	--amag-core-team-border: 0 0 0 1px solid var(--greyish-light);
	--amag-core-team-border-bottom: none;

	--amag-core-team-nameInputFontFamily: var(--font-head);
	--amag-core-team-nameInputFontSize: var(--default-font-size);
	--amag-core-team-nameInputLineHeight: var(--default-line-height);

	--amag-core-team-inputFontFamily: var(--font-text);
	--amag-core-team-inputFontSize: var(--default-font-size);
	--amag-core-team-inputLineHeight: var(--default-line-height);

	--amag-core-team-textarea-background: color(var(--greyish-light) a(25%));
	--amag-core-team-textarea-padding-top: 10px;
	--amag-core-team-select-border-color: var(--greyish-light);

	--amag-core-team-link-color: var(--primary-color);
	--amag-core-team-link-color-hover: var(--primary-color-hover);

	--amag-core-team-edit-button-border-color: var(--greyish-light);
	--amag-core-team-edit-button-icon: var(--iconEdit);
	--amag-core-team-delete-button-border-color: var(--primary-color);
	--amag-core-team-delete-button-icon: var(--iconDelete);
	--amag-core-team-add-button-background: color(var(--greyish-light) alpha(-30%));
	--amag-core-team-add-button-background-hover: color(var(--greyish-light) alpha(-30%));
	--amag-core-team-add-button-box-shadow: 0 5px 15px 0 color(var(--black) alpha(-80%));
	--amag-core-team-add-button-box-shadow-hover: 0 0px 15px 0 color(var(--black) alpha(-90%));
	--amag-core-team-add-button-color: var(--primary-color);

	--amag-core-team-editor-image-border-color: var(--greyish-light);
	--amag-core-team-editor-image-max-width: 300px;
	--amag-core-team-editor-image-max-width-mobile: 100px;
}


:root {
	--amag-core-locationHeight-brandHeight-categoriesHeight: {
		height: auto;
	};

	--amag-core-brandHeightcategoriesHeight-locationHeightcategoriesHeight: {
		height: 54px;;
		margin: auto;
		width: 1300px;
		position: relative;
		padding-top: 0;
	};

	--categoriesHeight: {
		height: auto;
	};

	--amag-core-location-filter: {
		clear: both;
		margin: 0;
		padding: 12px 12px 0;

		& .location {
			position: initial;
			opacity: 1;

			&.active {
				& button {
					&.button {
						font-family: var(--font-bold);
					}
				}
			}

			& .button {
				font-family: var(--font-text);
				font-size: 100%;
				box-sizing: border-box;
				display: inline-block;
				padding: 19px 20px;
				color: var(--black);
				transition: all 0.3s ease;
				border: none;
				cursor: pointer;
				background-color: transparent;
				border-color: transparent;
				padding-left: 0;

				&:hover:not([disabled]) {
					color: color(var(--black) alpha(50%));
					background-color: transparent;
					border-color: transparent;
				}

				&::after {
					content: '';
					display: none;
				}
			}

			& .brandFilter {
				left: 0;
				top: 38px;
				padding: 12px 12px 0;
			}
		}
	};

	--amag-core-team-filter: {
		clear: both;

		&.mobile {
			padding: 12px;
		}

		& select {
			margin-bottom: 0;

			@media (--screen-team-mobile) {
				&:first-child {
					margin-bottom: 12px;
				}
			}
		}
	};

	--amag-core-category-filter: {
		@apply --maxWidth;
		margin: auto;
		padding: 0;
		overflow: hidden;
		
		& li {
			margin: 0;
			opacity: 1;

			&:first-child {
				& button {
					padding-left: 0;
				}
			}

			&.active {
				opacity: 1;
				background-color: transparent;

				& button {
					font-family: var(--font-bold);
				}
			}

			& button {
				font-family: var(--font-text);
				box-sizing: border-box;
				display: inline-block;
				padding: 19px 20px;
				color: var(--black);
				transition: all 0.3s ease;
				border: none;
				cursor: pointer;
				background-color: transparent;
				border-color: transparent;

				&:hover:not([disabled]) {
					color: color(var(--black) alpha(50%));
					background-color: transparent;
					border-color: transparent;
				}
			}
		}
	};

	--amag-core-team-member-template: {
		& .buttonsBlock {
			height: 100%;
			opacity: 0;
			pointer-events: none;
			transition: all 0.3s ease;
			transform: translateY(-20px);
		}

		& .image {
			/* padding-bottom: 125.6%; */
			background-position: top;
			background-size: cover;
			background-repeat: no-repeat;
			height: 350px;
			margin-bottom: 15px;
			position: relative;

			&.editable:hover {
				& .buttonsBlock {
					opacity: 1;
					pointer-events: all;
					transform: translateY(0);
				}
			}
		}

		& button {
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				transform: scale(1.1);
			}

			&.deleteButton {
				background-color: var(--white);
			}
		}
	};

	--amag-core-team-confirm-dialog-modal: {
		& button[type="button"] {
			@apply --buttonReset;
			@apply --button;
			@apply --primary;
		}
	};

	--amag-core-team-modal-editor: {
		& button[type="button"] {
			@apply --buttonReset;
			@apply --button;
			@apply --primary;
		}
	};
}
