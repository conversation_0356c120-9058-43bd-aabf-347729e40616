@import theme(files/upload);

.container {
	margin: var(--files-upload-dropzone-margin, 0);
	background: var(--files-upload-dropzone-backgroundColor);
	border: dashed var(--files-upload-dropzone-borderWidth, 1px) var(--files-upload-dropzone-borderColor);
	cursor: pointer;
	border-radius: 5px ;
	transition: all 0.5s ease;
	width: 100%;
	height: 100%;
	min-height: 200px;
	box-sizing: border-box;

	&:hover {
		background: var(--files-upload-dropzone-hoverBackgroundColor);
	}

	& .dropzone {
		width: 100%;
		min-height: 200px;
		height: 100%;
		margin: 0;
		box-sizing: border-box;

		& .uploadMessage {
			font-family: var(--files-upload-dropzone-uploadMessageFontFamily);
			font-size: var(--files-upload-dropzone-uploadMessageFontSize);
			line-height: var(--files-upload-dropzone-uploadMessageLineHeight);
			color: var(--files-upload-dropzone-uploadMessageFontColor, inherit);
			font-weight: var(--files-upload-dropzone-uploadMessageFontWeight, inherit);
			text-align: center;
			vertical-align: top;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			border: none !important;
			padding: 0 10px;

			& span {
				display: block;
			}

			&.documentUploadMessage span:before {
				background: var(--files-upload-dropzone-documnetUploadMessageIcon) no-repeat center right transparent;
				width: 17px;
				height: 17px;
				content: ' ';
				display: inline-block;
				position: relative;
				margin-right: 10px;
				vertical-align: middle;
				top: -2px;
			}

			&.imageUploadMessage span:before {
				background: var(--files-upload-dropzone-imageUploadMessageIcon) no-repeat center right transparent;
				margin-right: 10px;
				position: relative;
				content: " ";
				width: 32px;
				height: 32px;
				display: inline-block;
				vertical-align: middle;
				top: -2px;
			}

			& svg {
				width: 50px;

				& .rc-progress-circle-path {
					stroke: var(--files-upload-dropzone-rcProgressCirclePath);
				}
			}
		}
	}

	& .rejectedFiles {
		padding: 20px 0;

		& ul {
			list-style-type: none;
			margin: 0 auto;
			padding: 0;
			width: 50%;
			background-color: rgba(255, 15, 15, 0.25);
			border-radius: 5px;
			box-shadow: 0 0px 6px 0 rgba(255, 0, 0, 1),
			0 0 0 5px rgba(255, 15, 15, 0.4);
			text-align: center;
			display: block;
			padding: 10px;

			& li {
				text-align: left;
				display: block;
				font-size: 12px;
				line-height: 1.5;
			}
		}

		@apply --upload-dropzone-rejected-files;
	}

	& .errorMessage {
		background-color: rgba(255, 15, 15, 0.25);
		border-radius: 5px;
		box-shadow: 0 0px 6px 0 rgba(255, 0, 0, 1),
		0 0 0 5px rgba(255, 15, 15, 0.4);
		text-align: center;
		display: block;
		width: 600px;
		margin: 0 auto;
		padding: 10px;

		@apply --editorDraft-headerImageContainer-container;
	}

	@apply --upload-dropzone-container;
}