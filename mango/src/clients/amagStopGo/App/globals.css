@import '../../../../node_modules/normalize.css/normalize.css';
@import '../../../../node_modules/font-awesome/css/font-awesome.css';
@import '../assets/fonts.css';
@import '../assets/variables.css';
@import '../assets/inputs.css';

html,
body {
	margin: 0;
	padding: 0;
	height: 100%;
	font-family: var(--font-text);
	font-size: var(--default-font-size);
	line-height: var(--default-line-height);

	@media(--screen-mobile){
		font-size: var(--default-font-size-mobile);
		line-height: var(--default-line-height-mobile);
	}
}

body {
	font-size-adjust: none;
	-moz-osx-font-smoothing: grayscale;
	-webkit-text-size-adjust: none;
	overflow-x: hidden;
	box-sizing: border-box;
	padding-top: 130px;
	transition: all 0.3s ease;

	@media (--screen-middle-max) {
		padding-top: 145px;
	}

	@media (--screen-tablet) {
		padding-top: 150px;
	}

	@media (--screen-mobile) {
		padding-top: 150px;
	}
}

a {
	outline: none;
	color: var(--black);
	text-decoration: none;
	transition: all 0.3s ease;

	&:hover {
		color: color(var(--black) alpha(70%));
	}
}

figure {
	margin: 0;
}

strong,
b {
	font-family: var(--font-head);
	font-weight: normal;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--font-head);
	font-weight: normal;
	margin: 0;
	padding: 0;
}

h1 {
	font-size: var(--h1-font-size);
	line-height: var(--h1-line-height);

	@media (--screen-mobile) {
		font-size: var(--h1-font-size-mobile);
		line-height: var(--h1-line-height-mobile);
	}
}

h2 {
	font-size: var(--h2-font-size);
	line-height: var(--h2-line-height);

	@media (--screen-mobile) {
		font-size: var(--h2-font-size-mobile);
		line-height: var(--h2-line-height-mobile);
	}
}

h3 {
	font-size: var(--h3-font-size);
	line-height: var(--h3-line-height);

	@media (--screen-mobile) {
		font-size: var(--h3-font-size-mobile);
		line-height: var(--h3-line-height-mobile);
	}
}

h4 {
	font-size: var(--h4-font-size);
	line-height: var(--h4-line-height);

	@media (--screen-mobile) {
		font-size: var(--h4-font-size-mobile);
		line-height: var(--h4-line-height-mobile);
	}
}

h5 {
	font-size: var(--h5-font-size);
	line-height: var(--h5-line-height);

	@media (--screen-mobile) {
		font-size: var(--h5-font-size-mobile);
		line-height: var(--h5-line-height-mobile);
	}
}

h6 {
	font-size: var(--h6-font-size);
	line-height: var(--h6-line-height);

	@media (--screen-mobile) {
		font-size: var(--h6-font-size-mobile);
		line-height: var(--h6-line-height-mobile);
	}
}

p {
	font-family: var(--font-text);
	font-size: var(--default-font-size);
	line-height: var(--default-line-height);
	color: var(--black);

	@media(--screen-mobile){
		font-size: var(--default-font-size-mobile);
		line-height: var(--default-line-height-mobile);
	}
}

* {
	outline: none;

	&::selection {
		background: var(--primary-color);
		color: var(--white);
	}
}

:global(.push10),
:global(.push20) {
	width: 100%;
	display: block;
}

:global(.push10) {
	height: 10px;
}

:global(.push20) {
	height: 20px;
}

:global {
	& .ReactModal__Body--open {
		overflow: hidden;
	}

	& .ReactModal__Overlay {
		position: fixed;
		overflow: scroll;
		left: 0;
		width: 100%;
		height: calc(100% - 94px);
		margin-top: 94px;
		transition: all 0.5s ease;
		top: -100%;
		background: color(black a(75%));
		z-index: 25;

		@media (--screen-middle-max) {
			height: calc(100% - 84px);
			margin-top: 84px;
		}

		@media (--screen-tablet) {
			height: calc(100% - 61px);
			margin-top: 61px;
		}

		&.ReactModal__Overlay--after-open {
			top: 0;
		}

		&.ReactModal__Overlay--after-close {
			top: -100%;
		}

		&.ReactModal__Overlay--before-close {
			top: -100%;
		}
	}
}

:global {
	/* Fix z-index issue when datepicker is inside modals */
	& .react-datepicker-popper {
		z-index: 99999 !important;
		position: fixed !important;
	}

	& .react-datepicker__tab-loop {
		z-index: 99999 !important;
		position: fixed !important;
	}

	& .react-datepicker {
		z-index: 99999 !important;
		position: relative !important;
	}

	/* Target all datepicker related elements */
	& [class*="react-datepicker"] {
		z-index: 99999 !important;
	}
}
