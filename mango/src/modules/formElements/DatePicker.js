/* @flow */
import React from 'react';
import ReactDatePicker from 'react-datepicker';
import s from 'react-datepicker/dist/react-datepicker-cssmodules.css';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import styles from './DatePicker.css';

const getDatePickerLocale = (locale: ?string): string => {
	switch (locale) {
		case 'fr':
			return 'fr-CH';
		case 'it':
			return 'it';
		case 'de':
			return 'de-CH';
		default:
			return locale || 'de-CH';
	}
};

type Props = {
	value: any,
	onChange: Function,
	placeholderText?: ?string,
	minDate?: any,
	maxDate?: any,
	disabled?: boolean,
	showTimeSelect?: boolean,
	showTimeSelectOnly?: boolean,
	timeIntervals?: number,
	timeFormat?: string,
	dateFormat?: string,
	className?: string,
	containerClassName?: string,
	calendarClassName?: string,
	popperPlacement?:	| "auto"
		| "auto-left"
		| "auto-right"
		| "bottom"
		| "bottom-end"
		| "bottom-start"
		| "left"
		| "left-end"
		| "left-start"
		| "right"
		| "right-end"
		| "right-start"
		| "top"
		| "top-end"
		| "top-start",
	locale?: string,
	timeCaption?: string,
	fixedHeight?: boolean,
	onChangeRaw?: Function,
	rawValue?: string,
	onKeyDown?: Function,
	onBlur?: Function
};

function DatePicker({
	value,
	onChange,
	placeholderText,
	minDate,
	maxDate,
	disabled,
	showTimeSelect,
	showTimeSelectOnly,
	timeIntervals = 15,
	timeFormat,
	dateFormat,
	className,
	containerClassName,
	calendarClassName,
	popperPlacement,
	locale,
	timeCaption,
	fixedHeight,
	onChangeRaw,
	rawValue,
	onKeyDown,
	onBlur,
}: Props) {
	return (
		<div className={classNames(styles.container, containerClassName)}>
			<ReactDatePicker
				selected={value || null}
				value={rawValue}
				onChange={onChange}
				placeholderText={placeholderText}
				timeCaption={timeCaption}
				showYearDropdown
				locale={getDatePickerLocale(locale)}
				style={{ width: '100px' }}
				minDate={minDate}
				maxDate={maxDate}
				disabled={disabled}
				showTimeSelect={showTimeSelect}
				showTimeSelectOnly={showTimeSelectOnly}
				timeIntervals={timeIntervals}
				timeFormat={timeFormat}
				dateFormat={dateFormat}
				className={className}
				calendarClassName={calendarClassName}
				popperPlacement={popperPlacement}
				fixedHeight={fixedHeight}
				onChangeRaw={onChangeRaw}
				onKeyDown={onKeyDown}
				onBlur={onBlur}
				portalId="datepicker-portal"
			/>
		</div>
	);
}

export default (withStyles(s, styles)(DatePicker): ReactComponent<Props>);
