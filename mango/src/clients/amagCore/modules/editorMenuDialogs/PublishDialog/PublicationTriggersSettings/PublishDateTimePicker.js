/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import moment from 'moment';
import combineHOCs from '../../../../../../tango/hoc/combineHOCs';
import { DateTimePicker } from '../../../../../../modules/formElements';
import { localize } from '../../../../../../modules/i18n';
import { LocalizeProps } from '../../../../../../modules/i18n/types';
import { isInfiniteDate } from './Utils';
import styles from './DateTimePicker.css';
import { namespace } from '../i18n';

type ExternalProps = {
	value: ?string,
	onChange: Function,
	className?: string,
}

type InternalProps = LocalizeProps;

type Props = ExternalProps & InternalProps;

function PublishDateTimePicker(props: Props) {
	const {
		value,
		onChange,
		className,
		t,
	} = props;

	if (value && isInfiniteDate(value) === false) {
		return (
			<div className={className}>
				<DateTimePicker
					value={value ? moment(value) : undefined}
					onChange={newValue =>
							onChange(newValue ? newValue.toISOString() : undefined)}
					minDate={moment()}
					minutesStep={15}
					containerClassName={styles.dateTimePickerContainer}
					className={styles.dateTimePickerInput}
					popperPlacement="top"
				/>
				<button
					className={styles.clearButton}
					type="button"
					onClick={() => onChange(undefined)}
				>
					Clear
				</button>
			</div>
		);
	}

	/* eslint-disable jsx-a11y/no-static-element-interactions */
	return (
		<div
			className={classNames(className, styles.notSetDateLabel)}
			onClick={() => onChange(moment().toISOString())}
		>
			{!value && t('now')}
			{value && isInfiniteDate(value) && t('never')}
		</div>);
}

const withHocs = combineHOCs([
	localize(namespace),
	withStyles(styles),
]);

export default (withHocs(PublishDateTimePicker): ReactComponent<ExternalProps>);
