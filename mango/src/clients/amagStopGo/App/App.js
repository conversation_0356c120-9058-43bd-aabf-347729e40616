/* @flow */
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { List, Map } from 'immutable';
import { Route, withRouter, Switch } from 'react-router-dom';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { connect } from 'react-redux';
import {
	RoutingProvider,
	Routes,
	ScrollToTop,
	Redirect,
} from '../../../tango/routing';
import guessLocale from '../../../tango/client-locale/guessLocale';
import type { RouteProps } from '../../../tango/routing/types';
import { LocaleProvider } from '../../../modules/i18n';
import { CALLBACK_PATH } from '../../../modules/auth0/constants';
import { getClientLocales } from '../../../modules/config/selectors';
import phrases from '../assets/phrases';
import GTM from '../../../modules/googleTagManager';
import { CancelRequestedLoginImmediately } from '../../../modules/auth0/CancelRequestedLoginImmediately';
import { getBrand } from '../../amagCore/modules/config/selectors';
import Loader from '../modules/Loader';
import { Auth0LoginHandler } from '../../../modules/auth0/Auth0LoginHandler';
import { createRoutingConfig } from './createRoutingConfig';
import { NavigationContainer } from '../modules/Navigation/NavigationContainer';
import { HtmlLocaleHelmet } from '../../../modules/helmet/HtmlLocaleHelmet';
import {
	getDealerLanguages,
	getGoogleTagManagerDataLayer,
	makeGetDealerBrandWebsiteConfig,
	getDealerIsCustomDomain,
	getDealerId,
	getDealerHasNewRetailerLayout,
	getDealerIsAmagRetailer,
	makeGetDealerName,
} from '../../amagCore/modules/DealerConfigProvider/selectors';
import getActualLanguages from '../../amagCore/modules/dealer-config/getActualLanguages';
import getDefaultLocale from '../../amagCore/modules/dealer-config/getDefaultLocale';
import { getLocale } from '../../../modules/i18n/selectors';
import { loadCustomScript } from '../../amagCore/modules/custom-script/CustomScript';
import { Footer } from '../modules/Footer/Footer';
import { DoubleClickFloodlightTracker } from '../../../modules/doubleclick-floodlight';
import { HELMET_LINKS, PAGE_TITLE } from './constants';
import { GoogleConversionGlobalTracking } from '../../amagCore/modules/tracking/GoogleConversionGlobalTracking';
import { GoogleConversionTracking } from '../modules/tracking';
import { AMAGRetailerGoogleTracking } from '../../amagCore/modules/tracking/AmagRetailerGoogleTracking';
import { OneTrustScript } from '../../amagCore/modules/onetrust';
import g from './globals.css';
import VWO from '../../../modules/VWO';

type Props = {
	nonce: string,
};

type InternalProps = {
	brand: string,
	languages: List<string>,
	clientLocales: List<string>,
	dataLayer: Array<Object>,
	siteTitle: string,
	doubleClickFloodlight: string,
	isCustomDomain: boolean,
	dealerId: string,
	dealerName: string,
	hasNewRetailerLayout: boolean,
	isAmagRetailer: boolean,
};

function App(props: Props & InternalProps) {
	const {
		brand,
		nonce,
		clientLocales,
		languages,
		dataLayer,
		isCustomDomain,
		siteTitle,
		doubleClickFloodlight,
		dealerId,
		dealerName,
		hasNewRetailerLayout,
		isAmagRetailer,
	} = props;

	const basePath = isCustomDomain ? '' : '/:dealerLabel';
	const routingConfig = createRoutingConfig(
		brand,
		basePath,
		hasNewRetailerLayout,
		isAmagRetailer,
	);

	const actualLanguages = getActualLanguages(languages);
	const appDefaultLocale = getDefaultLocale(languages);

	const availableLocales = actualLanguages ? actualLanguages.toJS() : ['de'];
	const defaultLocale = guessLocale(
		availableLocales,
		clientLocales,
		appDefaultLocale,
	);

	const seoTitle = siteTitle || dealerName;
	const defaultTitle = `${seoTitle} | ${PAGE_TITLE}`;
	const titleTemplate = `%s | ${dealerName} ${PAGE_TITLE}`;

	return (
		<React.Fragment>
			<GTM
				nonce={nonce}
				id="GTM-KPFCNKNJ"
				dataLayer={dataLayer}
				waitForConsent
			/>
			<OneTrustScript nonce={nonce}>
				<DoubleClickFloodlightTracker id={doubleClickFloodlight} />
				{isAmagRetailer && <AMAGRetailerGoogleTracking nonce={nonce} />}
				<GoogleConversionGlobalTracking nonce={nonce} />
				<GoogleConversionTracking nonce={nonce} dealerId={dealerId} />
			</OneTrustScript>
			<RoutingProvider config={routingConfig} />
			<CancelRequestedLoginImmediately />
			<Switch>
				<Route
					exact
					path={basePath || '/'}
					render={({ match }: RouteProps) => (
						<Redirect
							to="home"
							params={{
								locale: defaultLocale,
								dealerLabel: match.params.dealerLabel || '',
							}}
						/>
					)}
				/>
				<Route
					exact
					path={CALLBACK_PATH}
					render={() => (
						<React.Fragment>
							<Auth0LoginHandler />
							<Loader />
						</React.Fragment>
					)}
				/>
				<Route
					path={`${basePath}/:locale`}
					render={({ match }: RouteProps) => (
						<LocaleProvider
							routerLocale={match.params.locale}
							defaultLocale={defaultLocale}
							availableLocales={availableLocales}
							phrases={phrases}
						>
							<React.Fragment>
								<HtmlLocaleHelmet />
								<Helmet
									titleTemplate={titleTemplate}
									defaultTitle={defaultTitle}
									link={HELMET_LINKS}
									script={loadCustomScript(dealerId, nonce)}
								/>
								<VWO nonce={nonce} />
								<ScrollToTop />
								<NavigationContainer />

								<Routes config={routingConfig} />
								<Footer />
								{/* Portal container for datepicker to render outside modals */}
								<div id="datepicker-portal" />
							</React.Fragment>
						</LocaleProvider>
					)}
				/>
			</Switch>
		</React.Fragment>
	);
}

const makeMapStateToProps = () => {
	const getDealerName = makeGetDealerName();
	const getDealerBrandWebsiteConfig = makeGetDealerBrandWebsiteConfig();

	return (state: ReduxState) => {
		const dealerBrandWebsiteConfig =
			getDealerBrandWebsiteConfig(state)('stopgo') || Map();
		const languages = getDealerLanguages(state);
		const locale = getLocale(state) || getDefaultLocale(languages);

		return {
			dealerId: getDealerId(state),
			dealerName: getDealerName(state)(locale),
			dealerBrandWebsiteConfig,
			languages,
			clientLocales: getClientLocales(state),
			brand: getBrand(state),
			isCustomDomain: getDealerIsCustomDomain(state),
			dataLayer: getGoogleTagManagerDataLayer(state),
			siteTitle: dealerBrandWebsiteConfig.getIn(['site-titles', locale], ''),
			doubleClickFloodlight: dealerBrandWebsiteConfig.get(
				'visit-doubleclick-floodlight-id',
			),
			hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
			isAmagRetailer: getDealerIsAmagRetailer(state),
		};
	};
};

export default withRouter(withStyles(g)(connect(makeMapStateToProps)(App)));
