@import theme(editorMenu/publishDialog);

:root {
	--editorMenu-publishDialog-publicationSettingsIcon: url('\./Icons/clock.svg');
	--editorMenu-publishDialog-visibilityIcon: url('\./Icons/layout.svg');
	--editorMenu-publishDialog-flagIcon: url('\./Icons/label.svg');
	--editorMenu-publishDialog-highlightIcon: url('\./Icons/slider.svg');
	--editorMenu-publishDialog-positionIcon: url('\./Icons/position.svg');
	--editorMenu-publishDialog-errorPositionIcon: url('\./Icons/errorPosition.svg');
}

.publishDialog {
	min-width: 300px;
	max-width: 470px !important;
	width: 470px;
	font-family: var(--editorMenu-publishDialog-fontFamily);
	font-size: var(--editorMenu-publishDialog-fontSize);
	line-height: var(--editorMenu-publishDialog-lineHeight);

	/* Fix datepicker z-index in publish dialog */
	& :global(.react-datepicker-popper) {
		z-index: 99999 !important;
		position: fixed !important;
	}

	& :global(.react-datepicker) {
		z-index: 99999 !important;
	}

	& :global([class*="react-datepicker"]) {
		z-index: 99999 !important;
	}

	& input[type="text"],
	& input[type="number"],
	& textarea {
		margin: 10px 0;

		@apply --editorMenu-publishDialog-input;
	}

	& .metaDescription {
		& textarea {
			margin-bottom: 0;
		}

		font-family: var(--editorMenu-publishDialog-hintFontFamily);
		color: var(--editorMenu-publishDialog-hintColor);
		font-size: var(--editorMenu-publishDialog-hintFontSize);
		line-height: var(--editorMenu-publishDialog-hintLineHeight);
		margin: 0 0 10px 0;
	}

	& .publishDialogButton {
		margin: 15px 0;
		width: 100%;
		max-width: none;
	}
}

.hint {
	font-family: var(--editorMenu-publishDialog-hintFontFamily);
	color: var(--editorMenu-publishDialog-hintColor);
	font-size: var(--editorMenu-publishDialog-hintFontSize);
	line-height: var(--editorMenu-publishDialog-hintLineHeight);
	display: inline-block;
	margin: 6px 0 10px 0;

	&.errorHint {
		color: red;
	}
}

.settings {
	margin: 10px 0;
	display: flex;
	flex-wrap: wrap;
}

.settingsIcon {
	height: 14px;
	width: 14px;
	margin-right: 5px;
	display: inline-block;
	background-size: 14px 14px;
	background-repeat: no-repeat;
	background-position: center center;

	@apply --editorMenu-publishDialog-settingsIcon;
}

.publicationSettingsIcon {
	background-image: var(--editorMenu-publishDialog-publicationSettingsIcon);
}

.visibilityIcon {
	background-image: var(--editorMenu-publishDialog-visibilityIcon);
}

.flagIcon {
	background-image: var(--editorMenu-publishDialog-flagIcon);
}

.highlightIcon {
	background-image: var(--editorMenu-publishDialog-highlightIcon);
}

.positionIcon {
	background-image: var(--editorMenu-publishDialog-positionIcon);
	margin-top: 12px;
}

.errorPositionIcon {
	background-image: var(--editorMenu-publishDialog-errorPositionIcon);
	margin-top: 12px;
}

.settingsContent {
	flex: 1;
	flex-grow: 1;
}

.separator {
	border-bottom: 1px solid var(--editorMenu-publishDialog-labelColor);
	margin: 10px 0;
}

.label {
	color: var(--editorMenu-publishDialog-labelColor);
}

.editButton,
.editButton:hover {
	all: initial;
	color: var(--editorMenu-publishDialog-hightlightValueColor);
	display: inline-block;
	margin: 0 3px;
	cursor: pointer;
	font-family: inherit;
	font-size: inherit;
}

.error {
	color: color(var(--error-color));
	font-weight: 700;
	font-size: 12px;
	text-transform: uppercase;
}
